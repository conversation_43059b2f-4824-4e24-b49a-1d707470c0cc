"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
  VisibilityState,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, Eye, Pencil, Trash2, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Dialog<PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from "@/components/ui/select"
import { UIUser } from "@/types/user"
import { Role } from "@/types/role"
import { UpdateConfirmationDialog } from "@/components/ui/update-confirmation-dialog"
import { hasChanges, extractUserData } from "@/lib/change-detection"
import { toast } from "sonner"
import { useActivateUser } from "@/hooks/use-activate-user"
import { Switch } from "@/components/ui/switch"

interface PaginationProps {
  page: number
  pageSize: number
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
}

interface UserManagementProps {
  users: UIUser[]
  roles: Role[]
  onCreateUser: (user: Omit<UIUser, "id" | "created_at">) => void
  onUpdateUser: (id: string, user: Partial<UIUser>) => void
  onDeleteUser: (id: string) => void
  onRefresh?: () => void
  pagination?: PaginationProps
  searchQuery?: string
  onSearchChange?: (query: string) => void
}

export function UserManagement({ users, roles, onUpdateUser, onDeleteUser, onRefresh, pagination, searchQuery, onSearchChange }: UserManagementProps) {
  const availableStatuses = [
    { value: "active", label: "Actiu" },
    { value: "inactive", label: "Inactiu" }
  ];
  const { activateOrDeactivateUser, loading: activateLoading } = useActivateUser()
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [isEditDialogOpen, setIsEditDialogOpen] = React.useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false)
  const [isViewInfoDialogOpen, setIsViewInfoDialogOpen] = React.useState(false)
  const [selectedUser, setSelectedUser] = React.useState<UIUser | null>(null)
  const [originalUserData, setOriginalUserData] = React.useState<any>(null)
  const [showConfirmDialog, setShowConfirmDialog] = React.useState(false)
  const [formData, setFormData] = React.useState({
    name: "",
    email: "",
    role: "",
    status: "active" as "active" | "inactive"
  })

  React.useEffect(() => {
    if (!isEditDialogOpen || !selectedUser) return;
    const userData = {
      name: selectedUser.name || "",
      email: selectedUser.email || "",
      role: selectedUser.role || "",
      status: selectedUser.status || "active"
    }
    setFormData(userData)
    setOriginalUserData(extractUserData(selectedUser))
  }, [isEditDialogOpen, selectedUser])

  const columns: ColumnDef<UIUser>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Nom
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => (
        <div
          className="font-medium pl-4 cursor-pointer hover:text-blue-600 hover:underline"
          onClick={() => {
            setSelectedUser(row.original)
            setIsViewInfoDialogOpen(true)
          }}
        >
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "email",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Correu electrònic
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => <div>{row.getValue("email")}</div>,
    },
    {
      accessorKey: "role",
      header: 'Rol',
      cell: ({ row }) => {
        const role = row.getValue("role") as string
        return (
          <Badge variant="outline" className="text-xs">
            {role}
          </Badge>
        )
      },
    },
    {
      accessorKey: "status",
      header: 'Estat',
      cell: ({ row }) => {
        const status = row.getValue("status") as string
        return (
          <Badge
            variant={status === "active" ? "default" : "secondary"}
            className="text-xs"
          >
            {status === "active" ? "Actiu" : "Inactiu"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "created_at",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Creat el
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("created_at"))
        return <div>{date.toLocaleDateString()}</div>
      },
    },
    {
      accessorKey: "actions",
      header: ({ column }) => {
        return 'Accions'
      },
      cell: ({ row }) => {
        const user = row.original
        return (
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size={"icon"}
              onClick={() => {
                setSelectedUser(user)
                setIsEditDialogOpen(true)
              }}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size={"icon"}
              onClick={() => {
                setSelectedUser(user)
                setIsDeleteDialogOpen(true)
              }}
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </Button>
          </div>
        )
      }
    }
  ]

  const table = useReactTable({
    data: users,
    columns: columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    ...(pagination ? {} : { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    ...(pagination ? {
      manualPagination: true,
      pageCount: Math.ceil(pagination.totalCount / pagination.pageSize),
    } : {}),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      ...(pagination ? {
        pagination: {
          pageIndex: pagination.page - 1, // TanStack uses 0-based indexing
          pageSize: pagination.pageSize,
        },
      } : {}),
    },
  })

  const handleUpdateUser = () => {
    if (!selectedUser) return

    // Check if there are any changes
    if (!hasChanges(originalUserData, formData)) {
      toast.info('No s\'han detectat canvis')
      return
    }

    // Show confirmation dialog
    setShowConfirmDialog(true)
  }

  const handleConfirmUpdate = async () => {
    if (!selectedUser) return

    try {
      // Update role if changed
      if (formData.role !== originalUserData.role) {
        onUpdateUser(selectedUser.id, {
          role: formData.role,
        })
      }

      // Update status if changed
      if (formData.status !== originalUserData.status) {
        await activateOrDeactivateUser(selectedUser.id, formData.status === 'active')
      }

      setIsEditDialogOpen(false)
      setSelectedUser(null)
      setShowConfirmDialog(false)
      
      // Refresh the user list to get updated data
      if (onRefresh) {
        onRefresh()
      }
    } catch (error) {
      console.error('Error updating user:', error)
      setShowConfirmDialog(false)
    }
  }

  const handleDeleteUser = () => {
    if (selectedUser) {
      onDeleteUser(selectedUser.id)
      setIsDeleteDialogOpen(false)
      setSelectedUser(null)
    }
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Cercar usuaris..."
          value={searchQuery || ((table.getColumn("name")?.getFilterValue() as string) ?? "")}
          onChange={(event) => {
            if (onSearchChange) {
              onSearchChange(event.target.value)
            } else {
              table.getColumn("name")?.setFilterValue(event.target.value)
            }
          }}
          className="max-w-sm"
        />
        <div className="flex items-center space-x-2">
          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columnes <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu> */}
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Editar usuari</DialogTitle>
            <DialogDescription>
              Edita el rol i l'estat de l'usuari.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Nom
              </Label>
              <Input
                id="edit-name"
                disabled={true}
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-role" className="text-right">
                Rol
              </Label>
              <Select
                value={formData.role}
                onValueChange={(value) => setFormData({ ...formData, role: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Seleccionar un rol" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.name}>
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-status" className="text-right">
                Estat
              </Label>
              <div className="col-span-3 flex items-center space-x-2">
                <Switch
                  checked={formData.status === 'active'}
                  onCheckedChange={(checked) => setFormData({ ...formData, status: checked ? 'active' : 'inactive' })}
                  disabled={activateLoading}
                />
                <Label htmlFor="edit-status" className="text-sm">
                  {formData.status === 'active' ? 'Actiu' : 'Inactiu'}
                </Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel·lar
            </Button>
            <Button type="submit" onClick={handleUpdateUser} disabled={activateLoading}>
              {activateLoading ? 'Actualitzant...' : 'Desar canvis'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Update Confirmation Dialog */}
      <UpdateConfirmationDialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        onConfirm={handleConfirmUpdate}
        title="Confirmar Actualització d'Usuari"
        description="Estàs segur que vols actualitzar aquest usuari? Aquesta acció desarà tots els teus canvis."
        confirmText="Actualitzar Usuari"
        cancelText="Cancel·lar"
      />

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Eliminar usuari</DialogTitle>
            <DialogDescription>
              {`Estàs segur que vols eliminar l'usuari ${selectedUser?.name || ''}?`}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel·lar
            </Button>
            <Button variant="destructive" onClick={handleDeleteUser}>
              Eliminar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Info Dialog */}
      <Dialog open={isViewInfoDialogOpen} onOpenChange={setIsViewInfoDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Informació de l'usuari</DialogTitle>
            <DialogDescription>
              Detalls de l'usuari.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                ID:
              </Label>
              <div className="col-span-3">
                {selectedUser?.id}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                Nom:
              </Label>
              <div className="col-span-3">
                {selectedUser?.name}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                Correu electrònic:
              </Label>
              <div className="col-span-3">
                {selectedUser?.email}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                Rol:
              </Label>
              <div className="col-span-3">
                <Badge variant="secondary">
                  {selectedUser?.role}
                </Badge>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                Estat:
              </Label>
              <div className="col-span-3">
                <Badge variant={selectedUser?.status === "active" ? "default" : "secondary"}>
                  {selectedUser?.status === "active" ? "Actiu" : "Inactiu"}
                </Badge>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                Data de creació:
              </Label>
              <div className="col-span-3">
                {selectedUser?.created_at ? new Date(selectedUser.created_at).toLocaleString('ca-ES', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                }) : 'N/A'}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                Última actualització:
              </Label>
              <div className="col-span-3">
                {selectedUser?.updated_at ? new Date(selectedUser.updated_at).toLocaleString('ca-ES', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                }) : 'N/A'}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewInfoDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No hi ha resultats.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {/* Pagination Controls */}
      {pagination && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            Mostrant {((pagination.page - 1) * pagination.pageSize) + 1} a{" "}
            {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} de{" "}
            {pagination.totalCount} entrades
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Files per pàgina</p>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  pagination.onPageSizeChange(Number(e.target.value))
                  pagination.onPageChange(1) // Reset to first page when changing page size
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Pàgina {pagination.page} de{" "}
              {Math.ceil(pagination.totalCount / pagination.pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la primera pàgina</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la pàgina anterior</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a la pàgina següent</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(Math.ceil(pagination.totalCount / pagination.pageSize))}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a l'última pàgina</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Fallback pagination for non-paginated mode */}
      {!pagination && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">

          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Anterior
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Següent
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
