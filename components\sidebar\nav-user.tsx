"use client"

import {
  BellIcon,
  CreditCardIcon,
  LogOutIcon,
  MoreVerticalIcon,
  UserCircleIcon,
} from "lucide-react"
import { signOut } from "next-auth/react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { signIn, useSession, getProviders } from "next-auth/react"
import { useEffect, useState } from "react"

import { useMe } from "@/hooks/use-me"
import { UserSessionStatus } from "../auth/user-session-status"

import { <PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { useTheme } from "next-themes"

export function NavUser({
  user,
}: {
  user: {
    name: string
    email: string
    avatar: string
  }
}) {
  const { isMobile } = useSidebar()
  const { setTheme, theme } = useTheme()

  const me = useMe()
  const { data: session, status } = useSession()
  const [isAccountModalOpen, setIsAccountModalOpen] = useState(false)

  useEffect(() => {
    console.log('Session:', session)
    console.log('Status:', status)
  }, [])

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <Avatar className="h-8 w-8 rounded-lg">
                <AvatarImage src={session?.user?.image || user.avatar} alt={session?.user?.name || "User"} />
                <AvatarFallback className="rounded-lg">
                  {(session?.user?.name || "User").split(" ").map((n) => n[0]).join("")}
                </AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{session?.user?.name || "User"}</span>
                <span className="truncate text-xs text-muted-foreground">
                  {session?.user?.email || "<EMAIL>"}
                </span>
              </div>
              <MoreVerticalIcon className="ml-auto size-4" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                <Avatar className="h-8 w-8 rounded-lg">
                  <AvatarImage src={session?.user?.image || user.avatar} alt={session?.user?.name || "User"} />
                  <AvatarFallback className="rounded-lg">
                    {(session?.user?.name || "User").split(" ").map((n) => n[0]).join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{session?.user?.name || "User"}</span>
                  <span className="truncate text-xs text-muted-foreground">
                    {session?.user?.email || "<EMAIL>"}
                  </span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={() => setIsAccountModalOpen(true)}>
                <UserCircleIcon />
                Compte
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => {
                const nextTheme = theme === "light" ? "dark" : theme === "dark" ? "system" : "light"
                setTheme(nextTheme)
              }}>
                {/* Cycle through light -> dark -> system */}
                {theme === "light" && <Sun className="h-4 w-4" />}
                {theme === "dark" && <Moon className="h-4 w-4" />}
                {theme === "system" && <Monitor className="h-4 w-4" />}
                {theme === "light" && "Tema: Clar"}
                {theme === "dark" && "Tema: Fosc"}
                {theme === "system" && "Tema: Sistema"}
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => signOut({ callbackUrl: '/login' })}
              className="hover:bg-red-600 focus:bg-red-600 focus:text-white">
              <LogOutIcon />
              Cerrar Sesión
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Account Info Modal */}
        <Dialog open={isAccountModalOpen} onOpenChange={setIsAccountModalOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Account Information</DialogTitle>
              <DialogDescription>
                User details and authentication information
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* User Info Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">User Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={session?.user?.image || user.avatar} alt={session?.user?.name || "User"} />
                        <AvatarFallback>
                          {(session?.user?.name || "User").split(" ").map((n) => n[0]).join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{session?.user?.name || "User"}</p>
                        <p className="text-sm text-muted-foreground">{session?.user?.email || "<EMAIL>"}</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div>
                      <label className="text-sm font-medium">Status:</label>
                      <p className="text-sm">{status}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Session Email:</label>
                      <p className="text-sm font-mono break-all">{session?.user?.email || 'N/A'}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Session Data Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Session Data</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                    {JSON.stringify(session, null, 2)}
                  </pre>
                </div>
              </div>

              {/* Tokens Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Authentication Tokens</h3>
                <div className="grid grid-cols-1 gap-4">
                  {session?.accessToken && (
                    <div>
                      <label className="text-sm font-medium">Access Token:</label>
                      <div className="bg-gray-50 p-3 rounded border">
                        <p className="text-xs font-mono break-all">{session.accessToken}</p>
                      </div>
                    </div>
                  )}

                  {session?.idToken && (
                    <div>
                      <label className="text-sm font-medium">ID Token:</label>
                      <div className="bg-gray-50 p-3 rounded border">
                        <p className="text-xs font-mono break-all">{session.idToken}</p>
                      </div>
                    </div>
                  )}

                  {session?.djangoAccessToken && (
                    <div>
                      <label className="text-sm font-medium">Django Access Token:</label>
                      <div className="bg-gray-50 p-3 rounded border">
                        <p className="text-xs font-mono break-all">{session.djangoAccessToken}</p>
                      </div>
                    </div>
                  )}

                  {session?.djangoRefreshToken && (
                    <div>
                      <label className="text-sm font-medium">Django Refresh Token:</label>
                      <div className="bg-gray-50 p-3 rounded border">
                        <p className="text-xs font-mono break-all">{session.djangoRefreshToken}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Environment Info */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Environment Information</h3>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <label className="font-medium">Environment:</label>
                      <p>{process.env.NODE_ENV}</p>
                    </div>
                    <div>
                      <label className="font-medium">Current URL:</label>
                      <p className="break-all">{typeof window !== 'undefined' ? window.location.href : 'N/A'}</p>
                    </div>
                    <div>
                      <label className="font-medium">User Agent:</label>
                      <p className="break-all">{typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A'}</p>
                    </div>
                    <div>
                      <label className="font-medium">Timestamp:</label>
                      <p>{new Date().toISOString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* ME Info */}
              {me && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">/users/me Data</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                      {JSON.stringify(me, null, 2)}
                    </pre>
                  </div>
                </div>
              )}

              <UserSessionStatus />
            </div>
          </DialogContent>
        </Dialog>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
