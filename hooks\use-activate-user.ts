import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import { DEFAULT_API_URL } from '@/constants/constants'
import { useAuthFetch } from './use-auth-fetch'

export function useActivateUser() {
    const { data: session } = useSession()
    const { authFetch } = useAuthFetch()
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const activateOrDeactivateUser = async (userId: string, isActive: boolean) => {
        if (!session?.djangoAccessToken) {
            throw new Error('Autenticació requerida. Torna a iniciar sessió.')
        }

        setLoading(true)
        setError(null)

        try {
            const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${session.djangoAccessToken}`
            }

            const response = await fetch(`${backendUrl}/users/${userId}/activate_or_deactivate/`, {
                method: 'PATCH',
                body: JSON.stringify({ is_active: isActive }),
                headers,
            })

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}))
                throw new Error(errorData.message || `No s'ha pogut ${isActive ? 'activar' : 'desactivar'} l'usuari: ${response.statusText}`)
            }

            const result = await response.json()
            toast.success(`Usuari ${isActive ? 'activat' : 'desactivat'} correctament!`)
            return result
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : `No s'ha pogut ${isActive ? 'activar' : 'desactivar'} l'usuari`
            setError(errorMessage)
            toast.error(errorMessage)
            throw err
        } finally {
            setLoading(false)
        }
    }

    return { activateOrDeactivateUser, loading, error }
}