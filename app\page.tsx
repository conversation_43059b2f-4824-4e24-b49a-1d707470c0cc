"use client"

import React, { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Plus, Search } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { NewslettersTable } from "@/components/table/newsletters-table"
import { useNewsletters } from "@/hooks/use-newsletters"
import { useBrands } from "@/hooks/use-brands"
import { useLanguages } from "@/hooks/use-languages"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DEFAULT_PAGE_SIZE } from "@/constants/constants"

interface Props { }

const Page: NextPage<Props> = ({ }) => {
  const router = useRouter()
  const { status } = useSession()
  const { brands } = useBrands()
  const { languages } = useLanguages()
  const [newslettersSearchQuery, setNewslettersSearchQuery] = useState("")
  const [debouncedNewslettersSearch, setDebouncedNewslettersSearch] = useState("")
  const [selectedStatus, setSelectedStatus] = useState<string>("")
  const [selectedLanguage, setSelectedLanguage] = useState<string>("")
  // Pagination state for newsletters
  const [newslettersPage, setNewslettersPage] = useState(1)
  const [newslettersPageSize, setNewslettersPageSize] = useState(DEFAULT_PAGE_SIZE)

  // Debounce newsletters search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedNewslettersSearch(newslettersSearchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [newslettersSearchQuery])

  // Reset newsletters page when search or filters change
  useEffect(() => {
    setNewslettersPage(1)
  }, [debouncedNewslettersSearch, selectedStatus, selectedLanguage])

  // Fetch newsletters with search and filters
  const {
    newsletters,
    loading: newslettersLoading,
    error: newslettersError,
    refetch: refetchNewsletters,
    totalCount: newslettersTotalCount,
    hasNext: newslettersHasNext,
    hasPrevious: newslettersHasPrevious
  } = useNewsletters({
    search: debouncedNewslettersSearch || undefined,
    status: selectedStatus && selectedStatus !== "all" ? selectedStatus : undefined,
    language: selectedLanguage && selectedLanguage !== "all" ? selectedLanguage : undefined,
    page: newslettersPage,
    pageSize: newslettersPageSize
  })

  const handleCreateNewsletter = () => {
    router.push('/newsletters/create')
  }

  // Show loading while session is being fetched
  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Carregant...</div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-8">
      {/* Newsletters Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Newsletters</h1>
            <p className="text-muted-foreground">
              Gestiona les newsletters de Grandvalira, Pal Arinsal i Ordino Arcalís
            </p>
          </div>
          <Button onClick={handleCreateNewsletter}>
            <Plus className="mr-2 h-4 w-4" />
            Crear Butlletí
          </Button>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Cercar newsletters..."
              value={newslettersSearchQuery}
              onChange={(e) => setNewslettersSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Tots els estats" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tots els estats</SelectItem>
                <SelectItem value="draft">Borrador</SelectItem>
                <SelectItem value="published">Publicat</SelectItem>
                <SelectItem value="scheduled">Programat</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Tots els idiomes" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tots els idiomes</SelectItem>
                {languages.map((language) => (
                  <SelectItem key={language.language} value={language.language}>
                    {language.language_display}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {newslettersError && (
          <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            Error: {newslettersError}
          </div>
        )}

        <NewslettersTable
          data={newsletters}
          loading={newslettersLoading}
          onRefresh={refetchNewsletters}
          pagination={{
            page: newslettersPage,
            pageSize: newslettersPageSize,
            totalCount: newslettersTotalCount,
            hasNext: newslettersHasNext,
            hasPrevious: newslettersHasPrevious,
            onPageChange: setNewslettersPage,
            onPageSizeChange: setNewslettersPageSize
          }}
        />
      </div>
    </div>
  )
}

export default Page