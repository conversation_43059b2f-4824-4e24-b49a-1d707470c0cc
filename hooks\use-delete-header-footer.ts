import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseDeleteHeaderFooterReturn {
  deleteHeaderFooter: (id: string) => Promise<void>
  loading: boolean
  error: string | null
}

export function useDeleteHeaderFooter(): UseDeleteHeaderFooterReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteHeaderFooter = useCallback(async (id: string): Promise<void> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    try {
      setLoading(true)
      setError(null)

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/blocks/delete-header-footer/${id}/`, {
        method: 'DELETE',
        headers,
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `No s'ha pogut eliminar la capçalera/peu: ${response.statusText}`)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'S\'ha produït un error'
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  return {
    deleteHeaderFooter,
    loading,
    error
  }
}
