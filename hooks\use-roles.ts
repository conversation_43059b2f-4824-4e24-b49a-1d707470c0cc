import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Role } from '@/types/role'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseRolesParams {
  search?: string
  page?: number
  pageSize?: number
}

interface CreateRoleRequest {
  name: string
  permissions: number[]
}

interface UpdateRoleRequest {
  name: string
  permissions: number[]
}

interface UseRolesReturn {
  roles: Role[]
  loading: boolean
  error: string | null
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  refetch: () => void
  createRole: (role: CreateRoleRequest) => Promise<Role>
  updateRole: (id: number, role: UpdateRoleRequest) => Promise<Role>
  deleteRole: (id: number) => Promise<void>
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useRoles(params: UseRolesParams = {}): UseRolesReturn {
  const { data: session } = useSession()
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrevious, setHasPrevious] = useState(false)

  const getAuthHeaders = useCallback(() => {
    return {
      'Content-Type': 'application/json',
      ...(session?.djangoAccessToken && { 'Authorization': `Bearer ${session.djangoAccessToken}` })
    }
  }, [session?.djangoAccessToken])

  const fetchRoles = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const queryParams = new URLSearchParams()
      if (params.search) queryParams.append('search', params.search)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

      const url = `${API_BASE_URL}/users/groups/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      const response = await fetch(url, {
        method: 'GET',
        headers: getAuthHeaders(),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to fetch roles: ${response.statusText}`)
      }

      const data = await response.json()
      
      // Handle both paginated and non-paginated responses
      if (data.results) {
        // Paginated response
        setRoles(data.results)
        setTotalCount(data.count || 0)
        setHasNext(!!data.next)
        setHasPrevious(!!data.previous)
      } else {
        // Non-paginated response (array)
        setRoles(Array.isArray(data) ? data : [])
        setTotalCount(Array.isArray(data) ? data.length : 0)
        setHasNext(false)
        setHasPrevious(false)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setRoles([])
      setTotalCount(0)
      setHasNext(false)
      setHasPrevious(false)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders, params.search, params.page, params.pageSize])

  const createRole = useCallback(async (role: CreateRoleRequest): Promise<Role> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`${API_BASE_URL}/users/create-group/`, {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(role),
    })

    if (!response.ok) {
      throw new Error(`Failed to create role: ${response.statusText}`)
    }

    const newRole: Role = await response.json()
    setRoles(prev => [...prev, newRole])
    return newRole
  }, [session?.djangoAccessToken, getAuthHeaders])

  const updateRole = useCallback(async (id: number, role: UpdateRoleRequest): Promise<Role> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`${API_BASE_URL}/users/update-group/${id}/`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(role),
    })

    if (!response.ok) {
      throw new Error(`Failed to update role: ${response.statusText}`)
    }

    const updatedRole: Role = await response.json()
    setRoles(prev => prev.map(r => r.id === id ? updatedRole : r))
    return updatedRole
  }, [session?.djangoAccessToken, getAuthHeaders])

  const deleteRole = useCallback(async (id: number): Promise<void> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    const response = await fetch(`${API_BASE_URL}/users/groups-delete/${id}/`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    })

    if (!response.ok) {
      throw new Error(`Failed to delete role: ${response.statusText}`)
    }

    setRoles(prev => prev.filter(r => r.id !== id))
  }, [session?.djangoAccessToken, getAuthHeaders])

  useEffect(() => {
    fetchRoles()
  }, [fetchRoles])

  return {
    roles,
    loading,
    error,
    totalCount,
    hasNext,
    hasPrevious,
    refetch: fetchRoles,
    createRole,
    updateRole,
    deleteRole
  }
}
