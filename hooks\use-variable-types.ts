import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { DEFAULT_API_URL } from '@/constants/constants'

export interface VariableType {
  id: string
  name: string
  display_name: string
  description: string
  field_type: string
  field_type_display: string
  ai_generated: boolean
  max_length: number | null
}

interface UseVariableTypesReturn {
  variableTypes: VariableType[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useVariableTypes(): UseVariableTypesReturn {
  const { data: session } = useSession()
  const [variableTypes, setVariableTypes] = useState<VariableType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchVariableTypes = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if user is authenticated
      if (!session) {
        setError('User not authenticated')
        setVariableTypes([])
        return
      }

      if (!session.djangoAccessToken) {
        setError('Django access token not available. Please try logging in again.')
        setVariableTypes([])
        return
      }

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/variables/list-variable-types/`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch variable types: ${response.statusText}`)
      }

      const data: VariableType[] = await response.json()
      setVariableTypes(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setVariableTypes([])
    } finally {
      setLoading(false)
    }
  }, [session])

  useEffect(() => {
    fetchVariableTypes()
  }, [fetchVariableTypes])

  return {
    variableTypes,
    loading,
    error,
    refetch: fetchVariableTypes
  }
}