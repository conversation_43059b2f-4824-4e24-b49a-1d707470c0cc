import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import { DEFAULT_API_URL } from '@/constants/constants'

export interface CreateTemplateData {
  name: string
  brand: string
  description: string
  is_active: boolean
  template_blocks: { block_id: string; order_position: number }[]
}

export function useCreateTemplate() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createTemplate = async (data: CreateTemplateData) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida. Torna a iniciar sessió.')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const response = await fetch(`${backendUrl}/templates/create-template/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `No s'ha pogut crear la plantilla: ${response.statusText}`)
      }

      toast.success('Plantilla creada correctament!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'No s\'ha pogut crear la plantilla'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { createTemplate, loading, error }
}

