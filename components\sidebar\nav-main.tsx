"use client"

import { MailIcon, PlusCircleIcon, type LucideIcon } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import Link from "next/link"
import { usePathname } from "next/navigation"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
  }[]
}) {
  const pathname = usePathname()
  return (
    <SidebarGroup>
      <SidebarGroupContent className="flex flex-col gap-2">
        <SidebarMenu>
          <SidebarMenuItem className="flex items-center gap-2">
            <SidebarMenuButton
              tooltip="Crear newsletter"
              className="min-w-8 bg-primary text-primary-foreground duration-200 ease-linear hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground"
            >
              <PlusCircleIcon />
              <span>Crear newsletter</span>
            </SidebarMenuButton>
            <Button
              size="icon"
              className="h-9 w-9 shrink-0 group-data-[collapsible=icon]:opacity-0"
              variant="outline"
            >
              <MailIcon />
              <span className="sr-only">Inbox</span>
            </Button>
          </SidebarMenuItem>
        </SidebarMenu>
        <SidebarMenu>
          {items.map((item, index) => (
            <SidebarMenuItem key={item.title}>
              <Link href={item.url}>
              <SidebarMenuButton tooltip={item.title} isActive={item.url === pathname}>
                {item.icon && <item.icon />}
                <span>{item.title}</span>
              </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

import {
  Blocks,
  FileText,
  LayoutTemplate,
  Newspaper,
  Settings,
} from 'lucide-react'
import { NavSecondary } from './nav-secondary'

export function AppNavMain() {
  return (
    <NavSecondary
      items={[
        {
          title: 'Plantilles',
          icon: LayoutTemplate,
          url: '/templates',
        },
        {
          title: 'Capçaleres i peus',
          icon: Blocks,
          url: '/blocks',
        },
        {
          title: 'Newsletters',
          icon: Newspaper,
          url: '/newsletters',
        },
        {
          title: 'Documents',
          icon: FileText,
          url: '/documents',
        },
        {
          title: 'Configuració',
          icon: Settings,
          url: '/configuration',
        },
      ]}
    />
  )
}
