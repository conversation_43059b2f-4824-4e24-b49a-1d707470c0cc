import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { User, CreateUserRequest, UpdateUserRequest } from '@/types/user'
import { useAuthFetch } from './use-auth-fetch'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseUsersParams {
  search?: string
  page?: number
  pageSize?: number
}

interface UseUsersReturn {
  users: User[]
  loading: boolean
  error: string | null
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  refetch: () => void
  createUser: (user: CreateUserRequest) => Promise<User>
  updateUser: (id: string, user: UpdateUserRequest) => Promise<User>
  deleteUser: (id: string) => Promise<void>
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useUsers(params: UseUsersParams = {}): UseUsersReturn {
  const { data: session } = useSession()
  const { authFetch } = useAuthFetch()
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrevious, setHasPrevious] = useState(false)

  const fetchUsers = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setError('Autenticació requerida')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const queryParams = new URLSearchParams()
      if (params.search) queryParams.append('search', params.search)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

      const url = `${API_BASE_URL}/users/users/${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
      const response = await authFetch(url, {
        method: 'GET',
      })

      if (!response.ok) {
        throw new Error(`No s'han pogut obtenir els usuaris: ${response.statusText}`)
      }

      const data = await response.json()
      
      // Handle both paginated and non-paginated responses
      if (data.results) {
        // Paginated response
        setUsers(data.results)
        setTotalCount(data.count || 0)
        setHasNext(!!data.next)
        setHasPrevious(!!data.previous)
      } else {
        // Non-paginated response (array)
        setUsers(Array.isArray(data) ? data : [])
        setTotalCount(Array.isArray(data) ? data.length : 0)
        setHasNext(false)
        setHasPrevious(false)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'S\'ha produït un error')
      setUsers([])
      setTotalCount(0)
      setHasNext(false)
      setHasPrevious(false)
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, authFetch, params.search, params.page, params.pageSize])

  const createUser = useCallback(async (user: CreateUserRequest): Promise<User> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    const response = await authFetch(`${API_BASE_URL}/users/create_temp/`, {
      method: 'POST',
      body: JSON.stringify(user),
    })

    if (!response.ok) {
      throw new Error(`No s'ha pogut crear l'usuari: ${response.statusText}`)
    }

    const newUser: User = await response.json()
    setUsers(prev => [...prev, newUser])
    return newUser
  }, [session?.djangoAccessToken, authFetch])

  const updateUser = useCallback(async (id: string, user: UpdateUserRequest): Promise<User> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    const response = await authFetch(`${API_BASE_URL}/users/update-user-group/${id}/`, {
      method: 'PUT',
      body: JSON.stringify(user),
    })

    if (!response.ok) {
      throw new Error(`No s'ha pogut actualitzar l'usuari: ${response.statusText}`)
    }

    const updatedUser: User = await response.json()
    setUsers(prev => prev.map(u => u.id === id ? updatedUser : u))
    return updatedUser
  }, [session?.djangoAccessToken, authFetch])

  const deleteUser = useCallback(async (id: string): Promise<void> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    const response = await authFetch(`${API_BASE_URL}/users/delete/${id}/`, {
      method: 'DELETE',
    })

    if (!response.ok) {
      throw new Error(`No s'ha pogut eliminar l'usuari: ${response.statusText}`)
    }

    setUsers(prev => prev.filter(u => u.id !== id))
  }, [session?.djangoAccessToken, authFetch])

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  return {
    users,
    loading,
    error,
    totalCount,
    hasNext,
    hasPrevious,
    refetch: fetchUsers,
    createUser,
    updateUser,
    deleteUser
  }
}
