"use client"

import Re<PERSON>, { useEffect, use<PERSON><PERSON><PERSON>, useState } from "react"
import { <PERSON><PERSON><PERSON>, GripVertical as GripVerticalIcon, Eye } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useBrands } from "@/hooks/use-brands"
import type { Block } from "@/types/block"
import { useBlocks } from "@/hooks/use-blocks"
import { useTemplateDetail } from "@/hooks/use-template-detail"
import { useUpdateTemplate } from "@/hooks/use-update-template"
import { toast } from "sonner"
import {
  DndContext,
  KeyboardSensor,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"


interface EditTemplateModalProps {
  templateId: string | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function EditTemplateModal({ templateId, open, onOpenChange, onSuccess }: EditTemplateModalProps) {
  const { brands } = useBrands()
  const [brandFilter, setBrandFilter] = useState<string>("")
  const { blocks, refetch } = useBlocks({ page: 1, pageSize: 100, brand: brandFilter || undefined })
  const { template } = useTemplateDetail(templateId || undefined)
  const { updateTemplate, loading } = useUpdateTemplate()

  const [formData, setFormData] = useState({
    name: "",
    brand: "",
    description: "",
    template_blocks: [] as { block_id: string; order_position: number }[],
  })

  useEffect(() => {
    if (!template) return
    setFormData({
      name: template.name,
      brand: template.brand,
      description: template.description || "",
      template_blocks: template.template_blocks
        .sort((a, b) => a.order_position - b.order_position)
        .map((tb) => ({ block_id: tb.block.id, order_position: tb.order_position })),
    })
    setBrandFilter(template.brand)
  }, [template, blocks])

  // Clear selected blocks when brand changes
  useEffect(() => {
    setFormData(prev => ({ ...prev, template_blocks: [] }))
  }, [formData.brand])

  const availableBlocks = useMemo(() => [...blocks].sort((a, b) => a.name.localeCompare(b.name)), [blocks])
  // Build live preview HTML keeping variables as placeholders
  const previewHtml = useMemo(() => {
    let html = ""
    for (const tb of formData.template_blocks) {
      const block = availableBlocks.find(b => b.id === tb.block_id)
      if (!block) continue
      let content = block.html_content || ""
      // Keep variables as placeholders - don't replace them with default values
      html += `\n<!-- Bloc: ${block.name} (#${tb.order_position}) -->\n` + content + "\n"
    }
    return html
  }, [formData.template_blocks, availableBlocks])

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(MouseSensor, { activationConstraint: { distance: 5 } }),
    useSensor(TouchSensor),
    useSensor(KeyboardSensor)
  )

  function handleDragEnd(event: DragEndEvent) {
    const { active, over } = event
    if (!active || !over) return

    setFormData((prev) => {
      const oldIndex = parseInt(String(active.id), 10)
      const newIndex = parseInt(String(over.id), 10)
      if (Number.isNaN(oldIndex) || Number.isNaN(newIndex) || oldIndex === newIndex) return prev
      const moved = arrayMove(prev.template_blocks, oldIndex, newIndex)
      return {
        ...prev,
        template_blocks: moved.map((tb, i) => ({ ...tb, order_position: i + 1 }))
      }
    })
  }

  // Helper component: sortable row for selected blocks table
  function SortableRow({
    id,
    idx,
    name,
    onRemove,
  }: {
    id: number
    idx: number
    name: string
    onRemove: () => void
  }) {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id })

    return (
      <TableRow
        ref={setNodeRef}
        data-dragging={isDragging}
        className="relative z-0 data-[dragging=true]:z-10 data-[dragging=true]:opacity-80"
        style={{ transform: CSS.Transform.toString(transform), transition }}
      >
        <TableCell className="w-8">
          <Button {...attributes} {...listeners} variant="ghost" size="icon" className="size-7 text-muted-foreground hover:bg-transparent">
            <GripVerticalIcon className="size-3" />
            <span className="sr-only">Arrossegar per reordenar</span>
          </Button>
        </TableCell>
        <TableCell className="w-10 text-muted-foreground text-xs">{idx + 1}</TableCell>
        <TableCell>
          <div className="font-medium">{name}</div>
        </TableCell>
        <TableCell className="text-right">
          <Button size="sm" variant="destructive" onClick={onRemove}>Treure</Button>
        </TableCell>
      </TableRow>
    )
  }

  const handleAddBlock = (blockId: string) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: [
        ...prev.template_blocks,
        { block_id: blockId, order_position: prev.template_blocks.length + 1 },
      ],
    }))
  }

  const handleRemoveBlock = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      template_blocks: prev.template_blocks
        .filter((_, i) => i !== index)
        .map((tb, i) => ({ ...tb, order_position: i + 1 })),
    }))
  }



  const handleSubmit = async () => {
    if (!templateId) return
    if (!formData.name.trim()) return toast.error('El nom és obligatori')
    if (!formData.brand) return toast.error('Selecciona una marca')

    try {
      await updateTemplate({ id: templateId, ...formData, is_active: true })
      onSuccess()
      onOpenChange(false)
    } catch (e) {
      console.error(e)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Pencil className="h-5 w-5" />
            Editar Plantilla: {template?.name}
          </DialogTitle>
          <DialogDescription>
            Actualitza la informació i els blocs de la plantilla
          </DialogDescription>
        </DialogHeader>

        {/* Información general arriba */}
        <Card className="m-1 p-0">
        <CardHeader className="mt-1 pt-1">
            <CardTitle>Informació General</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4 pb-3">
            <div className="space-y-2">
              <Label htmlFor="name-template">Nom *</Label>
              <Input
                id="name-template"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value.substring(0, 70) }))}
                placeholder="Introdueix el nom de la plantilla"
                maxLength={70}
              />
              <p className="text-xs text-muted-foreground">{formData.name.length}/70 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Marca *</Label>
              <Select
                value={formData.brand}
                onValueChange={(value) => { setBrandFilter(value); setFormData(prev => ({ ...prev, brand: value, template_blocks: [] })); }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una marca" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descripció</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value.substring(0, 150) }))}
                placeholder="Introdueix la descripció de la plantilla (opcional)"
                maxLength={150}
              />
              <p className="text-xs text-muted-foreground">{formData.description.length}/150 caràcters</p>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* Left Panel - Form */}
          <div className="space-y-6 lg:col-span-3">

            <Card>
              <CardHeader>
                <CardTitle>Blocs de la Plantilla</CardTitle>
                <CardDescription>
                  Afegeix, treu i ordena els blocs de contingut
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Disable until brand selected */}
                {!formData.brand && (
                  <div className="p-4 text-sm text-muted-foreground border rounded">
                    Selecciona una marca per veure i afegir blocs.
                  </div>
                )}

                {formData.brand && (
                  <div className="overflow-hidden rounded-lg border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[50%]">Nom del bloc</TableHead>
                          <TableHead>Marca</TableHead>
                          <TableHead className="text-right"></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {availableBlocks.length ? (
                          availableBlocks.map((b) => (
                            <TableRow key={b.id}>
                              <TableCell className="font-medium">{b.name}</TableCell>
                              <TableCell>{b.brand_name}</TableCell>
                              <TableCell className="text-right">
                                <Button size="sm" variant="outline" onClick={() => handleAddBlock(b.id)}>
                                  Afegir
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={3} className="text-center text-sm text-muted-foreground h-16">
                              No hi ha blocs disponibles per a la marca seleccionada.
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                )}

                {/* Selected blocks as sortable table */}
                {formData.template_blocks.length > 0 && (
                  <div className="overflow-hidden rounded-lg border">
                    <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
                      <Table>
                        <TableHeader className="sticky top-0 z-10 bg-muted">
                          <TableRow>
                            <TableHead className="w-8"></TableHead>
                            <TableHead className="w-10">#</TableHead>
                            <TableHead>Bloc</TableHead>
                            <TableHead className="text-right"></TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {/* Provide ids as indexes for SortableContext */}
                          <SortableContext items={formData.template_blocks.map((_, idx) => idx)} strategy={verticalListSortingStrategy}>
                            {formData.template_blocks.map((tb, idx) => {
                              const block = availableBlocks.find((b) => b.id === tb.block_id)
                              return (
                                <SortableRow
                                  key={`${tb.block_id}-${idx}`}
                                  id={idx}
                                  idx={idx}
                                  name={block?.name || tb.block_id}
                                  onRemove={() => handleRemoveBlock(idx)}
                                />
                              )
                            })}
                          </SortableContext>
                        </TableBody>
                      </Table>
                    </DndContext>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Preview */}
          <div
            className="space-y-6 lg:col-span-4 min-w-[300px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto"
            style={{ zIndex: 10 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Vista prèvia
                </CardTitle>
                <CardDescription>
                  Veu com es veurà la teva plantilla amb els blocs seleccionats
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 pb-0 pl-0 pr-0 min-w-[600px] max-w-[640px] shadow-inner">
                  {previewHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>Afegeix blocs per veure la previsualització</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel·lar
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Actualitzant...' : 'Actualitzar Plantilla'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

