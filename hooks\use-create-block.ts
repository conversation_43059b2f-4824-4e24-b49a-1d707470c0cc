import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'
import { DEFAULT_API_URL } from '@/constants/constants'

interface Variable {
  name: string
  variable_type_id: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

interface CreateBlockData {
  name: string
  brand: string
  html_content: string
  description: string
  variables: Variable[]
  is_active: boolean
}

interface UseCreateBlockReturn {
  createBlock: (data: CreateBlockData) => Promise<void>
  loading: boolean
  error: string | null
}

export function useCreateBlock(): UseCreateBlockReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createBlock = async (data: CreateBlockData) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required. Please log in again.')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL
      
      const response = await fetch(`${backendUrl}/blocks/create-block/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || `Failed to create block: ${response.statusText}`)
      }

      // Success - no need to return data, just complete
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'No s\'ha pogut crear el bloc'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }

  return {
    createBlock,
    loading,
    error
  }
}
