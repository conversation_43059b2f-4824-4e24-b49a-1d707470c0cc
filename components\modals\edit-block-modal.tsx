"use client"

import React, { useState, useEffect } from "react"
import { Pencil, Eye, Variable, Globe, Tag } from "lucide-react"
import { Block, BlockVariable } from "@/types/block"
import { useBrands } from "@/hooks/use-brands"
import { useUpdateBlock } from "@/hooks/use-update-block"
import { useVariableTypes } from "@/hooks/use-variable-types"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { HtmlEditor } from "@/components/ui/html-editor"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "sonner"
import { UpdateConfirmationDialog } from "@/components/ui/update-confirmation-dialog"
import { hasChanges, extractBlockData } from "@/lib/change-detection"

interface Variable {
  id?: string
  name: string
  variable_type_id: string
  default_value: {
    es: string
    ca: string
    fr: string
    en: string
  }
}

interface EditBlockModalProps {
  block: Block | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function EditBlockModal({ block, open, onOpenChange, onSuccess }: EditBlockModalProps) {
  const { brands, loading: brandsLoading } = useBrands()
  const { updateBlock, loading: updateLoading } = useUpdateBlock()
  const { variableTypes, loading: variableTypesLoading } = useVariableTypes()

  const [formData, setFormData] = useState({
    name: "",
    brand: "",
    html_content: "",
    description: "",
    variables: [] as Variable[],
    is_active: true
  })

  const [variables, setVariables] = useState<Variable[]>([])
  const [previewHtml, setPreviewHtml] = useState("")
  const [originalData, setOriginalData] = useState<any>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  // Helper function to get variable type name
  const getVariableTypeName = (variableTypeId: string) => {
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.display_name : "N/A"
  }

  // Initialize form data when block changes
  useEffect(() => {
    function blockVarToVariable(blockVar: BlockVariable): Variable {
      return {
        id: blockVar.id,
        name: blockVar.name,
        variable_type_id: blockVar.variable_type,
        default_value: blockVar.default_value
      }
    }

    if (block) {
      const blockData = {
        name: block.name,
        brand: block.brand,
        html_content: block.html_content,
        description: block.description || "",
        variables: block.variables.map((v) => blockVarToVariable(v)),
        is_active: block.is_active ?? true
      }

      setFormData(blockData)
      setOriginalData(extractBlockData(block))

      // Set variables from block
      setVariables(block.variables.map((v) => blockVarToVariable(v)))
    }
  }, [block])

  // Update preview HTML when content or variables change
  useEffect(() => {
    let html = formData.html_content

    // Replace variables in HTML with their default values (using Spanish as default)
    formData.variables.forEach((variable) => {
      const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
      html = html.replace(regex, variable.default_value.es || '')
    })

    setPreviewHtml(html)
  }, [formData.html_content, formData.variables])

  // Extract variables from HTML content (auto-detection like create modal)
  useEffect(() => {
    const variableRegex = /\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}/g
    const foundVariables = new Set<string>()
    let match

    while ((match = variableRegex.exec(formData.html_content)) !== null) {
      foundVariables.add(match[1])
    }

    // Update variables list
    const newVariables: Variable[] = Array.from(foundVariables).map(varName => {
      const existing = variables.find(v => v.name === varName)
      return existing || {
        name: varName,
        variable_type_id: variableTypes.length > 0 ? variableTypes[0].id : "",
        default_value: { es: '', ca: '', fr: '', en: '' }
      }
    })

    setVariables(newVariables)
    setFormData(prev => ({ ...prev, variables: newVariables }))
  }, [formData.html_content, variableTypes])

  const handleVariableChange = (varName: string, language: 'es' | 'ca' | 'fr' | 'en', value: string) => {
    const updatedVariables = variables.map(v =>
      v.name === varName
        ? { ...v, default_value: { ...v.default_value, [language]: value } }
        : v
    )

    setVariables(updatedVariables)
    setFormData(prev => ({ ...prev, variables: updatedVariables }))
  }

  const handleVariableTypeChange = (varName: string, variableTypeId: string) => {
    const updatedVariables = variables.map(v =>
      v.name === varName
        ? { ...v, variable_type_id: variableTypeId }
        : v
    )

    setVariables(updatedVariables)
    setFormData(prev => ({ ...prev, variables: updatedVariables }))
  }



  const handleSubmit = async () => {
    if (!block) return

    if (!formData.name.trim()) {
      toast.error('El nom del bloc és obligatori')
      return
    }

    if (!formData.brand) {
      toast.error('Si us plau, selecciona una marca')
      return
    }

    if (!formData.html_content.trim()) {
      toast.error('El contingut HTML és obligatori')
      return
    }

    // Check if there are any changes
    if (!hasChanges(originalData, formData)) {
      toast.info('No s\'han detectat canvis')
      return
    }

    // Show confirmation dialog
    setShowConfirmDialog(true)
  }

  const handleConfirmUpdate = async () => {
    if (!block) return

    try {
      await updateBlock({
        id: block.id,
        ...formData
      })
      onSuccess()
      onOpenChange(false)
      setShowConfirmDialog(false)
    } catch (error) {
      console.error('Error updating block:', error)
      setShowConfirmDialog(false)
    }
  }

  if (!block) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Pencil className="h-5 w-5" />
            Editar Bloc: {block.name}
          </DialogTitle>
          <DialogDescription>
            Actualitza la informació i el contingut del bloc
          </DialogDescription>
        </DialogHeader>

        {/* Información general arriba */}
        <Card>
          <CardHeader>
            <CardTitle>Informació General</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name-block">Nom *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value.substring(0, 70) }))}
                placeholder="Introdueix el nom del bloc"
                maxLength={70}
              />
              <p className="text-xs text-muted-foreground">{formData.name.length}/70 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Marca *</Label>
              <Select
                disabled={true}
                value={formData.brand}
                onValueChange={(value) => setFormData(prev => ({ ...prev, brand: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una marca" />
                </SelectTrigger>
                <SelectContent>
                  {brands.map((brand) => (
                    <SelectItem key={brand.id} value={brand.id}>
                      {brand.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descripció</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value.substring(0, 150) }))}
                placeholder="Introdueix la descripció del bloc (opcional)"
                rows={2}
                maxLength={150}
              />
              <p className="text-xs text-muted-foreground">{formData.description.length}/150 caràcters</p>
            </div>

            <div className="space-y-2">
              <Label>Configuració</Label>
              <div className="flex items-center space-x-2 mt-2">
                <Switch
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
                <Label className="text-sm">Actiu</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* Left Panel - Form */}
          <div className="space-y-6 lg:col-span-3">

            <Card>
              <CardHeader>
                <CardTitle>Contingut HTML</CardTitle>
                <CardDescription>
                  Escriu el teu contingut HTML. Utilitza {`{{ nomVariable }}`} per a variables.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <HtmlEditor
                  placeholder="Introdueix el teu contingut HTML aquí..."
                  value={formData.html_content}
                  onChange={(value) => setFormData(prev => ({ ...prev, html_content: value }))}
                  rows={20}
                />
              </CardContent>
            </Card>

            {/* Variables */}
            {variables.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Variable className="h-5 w-5" />
                    Variables del Bloc
                  </CardTitle>
                  <CardDescription>
                    Configura els valors per defecte per a les variables trobades al teu HTML
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {variables.map((variable, index) => (
                      <div key={variable.name} className="relative">
                        {/* Variable Header */}
                        <div className="flex items-center justify-between mb-4 pb-3 border-b">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900">
                                {variable.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </h4>
                              <p className="text-xs text-gray-500 font-mono">
                                {`{{ ${variable.name} }}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Select
                              value={variable.variable_type_id}
                              onValueChange={(value) => handleVariableTypeChange(variable.name, value)}
                              disabled={variableTypesLoading}
                            >
                              <SelectTrigger className="w-40">
                                <SelectValue placeholder={variableTypesLoading ? "Carregant..." : "Tipus"} />
                              </SelectTrigger>
                              <SelectContent>
                                {variableTypes.map((type) => (
                                  <SelectItem key={type.id} value={type.id}>
                                    <div className="flex flex-col">
                                      <span className="font-medium text-xs">{type.display_name}</span>
                                      <span className="text-xs text-muted-foreground">{type.field_type_display}</span>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        {/* Language Inputs */}
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-red-500" />
                              <Label htmlFor={`var-${variable.name}-es`} className="text-sm font-medium">
                                Espanyol
                              </Label>
                              <span className="text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded">ES</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-es`}
                              placeholder="Valor en espanyol"
                              value={variable.default_value.es}
                              onChange={(e) => handleVariableChange(variable.name, 'es', e.target.value)}
                              className="border-red-200 focus:border-red-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-yellow-600" />
                              <Label htmlFor={`var-${variable.name}-ca`} className="text-sm font-medium">
                                Català
                              </Label>
                              <span className="text-xs bg-yellow-100 text-yellow-700 px-1.5 py-0.5 rounded">CA</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-ca`}
                              placeholder="Valor en català"
                              value={variable.default_value.ca}
                              onChange={(e) => handleVariableChange(variable.name, 'ca', e.target.value)}
                              className="border-yellow-200 focus:border-yellow-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-blue-500" />
                              <Label htmlFor={`var-${variable.name}-fr`} className="text-sm font-medium">
                                Francès
                              </Label>
                              <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">FR</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-fr`}
                              placeholder="Valor en francès"
                              value={variable.default_value.fr}
                              onChange={(e) => handleVariableChange(variable.name, 'fr', e.target.value)}
                              className="border-blue-200 focus:border-blue-400"
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-green-500" />
                              <Label htmlFor={`var-${variable.name}-en`} className="text-sm font-medium">
                                Anglès
                              </Label>
                              <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">EN</span>
                            </div>
                            <Input
                              id={`var-${variable.name}-en`}
                              placeholder="Valor en anglès"
                              value={variable.default_value.en}
                              onChange={(e) => handleVariableChange(variable.name, 'en', e.target.value)}
                              className="border-green-200 focus:border-green-400"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Preview */}
          <div
            className="space-y-6 lg:col-span-4 min-w-[300px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto"
            style={{ zIndex: 10 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Vista prèvia
                </CardTitle>
                <CardDescription>
                  Veu com es veurà el teu bloc amb els valors de les variables
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 pb-0 pl-0 pr-0 min-w-[600px] max-w-[640px] shadow-inner">
                  {previewHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No hi ha contingut per previsualitzar</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel·lar
          </Button>
          <Button onClick={handleSubmit} disabled={updateLoading}>
            {updateLoading ? 'Actualitzant...' : 'Actualitzar Bloc'}
          </Button>
        </div>

        <UpdateConfirmationDialog
          open={showConfirmDialog}
          onOpenChange={setShowConfirmDialog}
          onConfirm={handleConfirmUpdate}
          title="Confirmar Actualització del Bloc"
          description="Estàs segur que vols actualitzar aquest bloc? Aquesta acció desarà tots els teus canvis."
          confirmText="Actualitzar Bloc"
          cancelText="Cancel·lar"
        />
      </DialogContent>
    </Dialog>
  )
}
