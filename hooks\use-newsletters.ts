import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { Newsletter, NewsletterResponse } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseNewslettersParams {
  search?: string
  status?: string
  language?: string
  page?: number
  pageSize?: number
}

interface UseNewslettersReturn {
  newsletters: Newsletter[]
  loading: boolean
  error: string | null
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  refetch: () => void
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useNewsletters(params: UseNewslettersParams = {}): UseNewslettersReturn {
  const { data: session } = useSession()
  const [newsletters, setNewsletters] = useState<Newsletter[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [hasNext, setHasNext] = useState(false)
  const [hasPrevious, setHasPrevious] = useState(false)

  const getAuthHeaders = useCallback(() => {
    return {
      'Content-Type': 'application/json',
      ...(session?.djangoAccessToken && { 'Authorization': `Bearer ${session.djangoAccessToken}` })
    }
  }, [session?.djangoAccessToken])

  const fetchNewsletters = useCallback(async () => {
    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const queryParams = new URLSearchParams()
      if (params.search) queryParams.append('search', params.search)
      if (params.status) queryParams.append('status', params.status)
      if (params.language) queryParams.append('language', params.language)
      if (params.page) queryParams.append('page', params.page.toString())
      if (params.pageSize) queryParams.append('page_size', params.pageSize.toString())

      const response = await fetch(`${API_BASE_URL}/newsletters/list/?${queryParams.toString()}`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      if (!response.ok) {
        throw new Error(`Failed to fetch newsletters: ${response.statusText}`)
      }

      const data: NewsletterResponse = await response.json()

      setNewsletters(data.results)
      setTotalCount(data.count)
      setHasNext(!!data.next)
      setHasPrevious(!!data.previous)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setNewsletters([])
      setTotalCount(0)
      setHasNext(false)
      setHasPrevious(false)
    } finally {
      setLoading(false)
    }
  }, [params.search, params.status, params.language, params.page, params.pageSize, session?.djangoAccessToken, getAuthHeaders])

  useEffect(() => {
    fetchNewsletters()
  }, [fetchNewsletters])

  return {
    newsletters,
    loading,
    error,
    totalCount,
    hasNext,
    hasPrevious,
    refetch: fetchNewsletters
  }
}
