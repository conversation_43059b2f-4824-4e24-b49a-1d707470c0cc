import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { TemplateDetail } from '@/types/template'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseTemplateDetailReturn {
  template: TemplateDetail | null
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useTemplateDetail(id?: string): UseTemplateDetailReturn {
  const { data: session } = useSession()
  const [template, setTemplate] = useState<TemplateDetail | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchDetail = useCallback(async () => {
    if (!id) return
    try {
      setLoading(true)
      setError(null)

      if (!session?.djangoAccessToken) {
        throw new Error('Usuari no autenticat')
      }

      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.djangoAccessToken}`
      }

      const response = await fetch(`${backendUrl}/templates/${id}/detail/`, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        throw new Error(`No s'ha pogut carregar la plantilla: ${response.statusText}`)
      }

      const data: TemplateDetail = await response.json()
      setTemplate(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'S\'ha produ\u00eft un error')
      setTemplate(null)
    } finally {
      setLoading(false)
    }
  }, [id, session?.djangoAccessToken])

  useEffect(() => {
    fetchDetail()
  }, [fetchDetail])

  return { template, loading, error, refetch: fetchDetail }
}

