"use client"

import React, { useState, useEffect } from "react"
import { Eye, Variable, Globe, Tag } from "lucide-react"
import { Block } from "@/types/block"
import { useVariableTypes } from "@/hooks/use-variable-types"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

interface ViewBlockModalProps {
  block: Block | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewBlockModal({ block, open, onOpenChange }: ViewBlockModalProps) {
  const [previewHtml, setPreviewHtml] = useState("")
  const { variableTypes, loading: variableTypesLoading } = useVariableTypes()

  // Helper function to get variable type name
  const getVariableTypeName = (variableTypeId: string) => {
    if (variableTypesLoading) return 'Loading...'
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.display_name : 'Unknown Type'
  }

  // Helper function to get variable type field type
  const getVariableTypeFieldType = (variableTypeId: string) => {
    if (variableTypesLoading) return ''
    const variableType = variableTypes.find(type => type.id === variableTypeId)
    return variableType ? variableType.field_type_display : ''
  }

  // Update preview HTML when block changes
  useEffect(() => {
    if (!block) return

    let html = block.html_content

    // Replace variables in HTML with their default values (using Spanish as default)
    if (block.variables && Array.isArray(block.variables)) {
      block.variables.forEach((variable) => {
        const regex = new RegExp(`\\{\\{\\s*${variable.name}\\s*\\}\\}`, 'g')
        html = html.replace(regex, variable.default_value.es || '')
      })
    }

    setPreviewHtml(html)
  }, [block])

  if (!block) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            {block.name}
          </DialogTitle>
          <DialogDescription>
            {block.description || 'Detalls del bloc i vista prèvia'}
          </DialogDescription>
        </DialogHeader>

        {/* Información general arriba */}
        <Card className="m-1 p-0">
        <CardHeader className="mt-1 pt-1">
            <CardTitle>Informació General</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-5 gap-4 pb-3">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Marca</Label>
              <p className="text-sm font-medium">{block.brand_name}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Estat</Label>
              <div className="flex gap-2 mt-1">
                <Badge variant={block.is_active ? 'default' : 'secondary'}>
                  {block.is_active ? 'Actiu' : 'Inactiu'}
                </Badge>
              </div>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Variables</Label>
              <p className="text-sm font-medium">{block.variables?.length || 0}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Creat</Label>
              <p className="text-sm">{block.created_by?.first_name} {block.created_by?.last_name}</p>
              <p className="text-xs text-muted-foreground">{new Date(block.created_at).toLocaleString()}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Actualitzat</Label>
              <p className="text-sm">{block.updated_by?.first_name} {block.updated_by?.last_name}</p>
              <p className="text-xs text-muted-foreground">{new Date(block.updated_at).toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-7 gap-6">
          {/* Left Panel - Variables */}
          <div className="space-y-6 lg:col-span-3">

            {/* Variables - Matching Edit Block Style */}
            {block.variables && block.variables.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Variable className="h-5 w-5" />
                    Variables del Bloc
                  </CardTitle>
                  <CardDescription>
                    Valors per defecte de les variables del bloc
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {block.variables.map((variable, index) => (
                      <div key={variable.id} className="relative">
                        {/* Variable Header */}
                        <div className="flex items-center justify-between mb-4 pb-3 border-b">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                              {index + 1}
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900">
                                {variable.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </h4>
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-blue-600 font-medium">
                              {getVariableTypeName(variable.variable_type)}
                            </span>
                            {getVariableTypeFieldType(variable.variable_type) && (
                              <span className="text-xs text-gray-400">
                                ({getVariableTypeFieldType(variable.variable_type)})
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Language Values - Read Only */}
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-red-500" />
                              <Label className="text-sm font-medium">
                                Espanyol
                              </Label>
                              <span className="text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded">ES</span>
                            </div>
                            <div className="p-2 bg-gray-50 border border-red-200 rounded text-sm">
                              {variable.default_value.es || 'Sense valor'}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-yellow-600" />
                              <Label className="text-sm font-medium">
                                Català
                              </Label>
                              <span className="text-xs bg-yellow-100 text-yellow-700 px-1.5 py-0.5 rounded">CA</span>
                            </div>
                            <div className="p-2 bg-gray-50 border border-yellow-200 rounded text-sm">
                              {variable.default_value.ca || 'Sense valor'}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-blue-500" />
                              <Label className="text-sm font-medium">
                                Francès
                              </Label>
                              <span className="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded">FR</span>
                            </div>
                            <div className="p-2 bg-gray-50 border border-blue-200 rounded text-sm">
                              {variable.default_value.fr || 'Sense valor'}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Globe className="h-3 w-3 text-green-500" />
                              <Label className="text-sm font-medium">
                                Anglès
                              </Label>
                              <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded">EN</span>
                            </div>
                            <div className="p-2 bg-gray-50 border border-green-200 rounded text-sm">
                              {variable.default_value.en || 'Sense valor'}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Panel - Preview */}
          <div className="space-y-6 lg:col-span-4 min-w-[300px] sticky top-6 max-h-[calc(100vh-48px)] overflow-y-auto" style={{ zIndex: 10 }}>
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Vista prèvia
                </CardTitle>
                <CardDescription>
                  Com es veu el bloc amb els valors de les variables
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg p-6 pb-0 pl-0 pr-0 min-w-[600px] max-w-[640px] shadow-inner">
                  {previewHtml ? (
                    <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                  ) : (
                    <div className="flex items-center justify-center h-full text-muted-foreground">
                      <div className="text-center">
                        <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p>No hi ha contingut per previsualitzar</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* HTML Source */}
            <Card>
              <CardHeader>
                <CardTitle>Codi HTML</CardTitle>
                <CardDescription>
                  Codi font del bloc
                </CardDescription>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto max-h-60">
                  <code>{block.html_content}</code>
                </pre>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Tancar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
